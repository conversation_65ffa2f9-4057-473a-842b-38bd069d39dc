using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using RevitPointCloudPlugin.Core.DataIngestion;
using RevitPointCloudPlugin.Core.SpatialIndex;
using RevitPointCloudPlugin.Utils;

namespace RevitPointCloudPlugin.Core.Streaming
{
    /// <summary>
    /// Implementation of out-of-core streaming manager for point cloud data
    /// </summary>
    public class StreamingManager : IStreamingManager, IMemoryConsumer
    {
        private readonly StreamingConfig _config;
        private readonly IStreamingCache _cache;
        private readonly ConcurrentDictionary<string, StreamingChunk> _activeChunks;
        private readonly SemaphoreSlim _loadingSemaphore;
        private PointCloudData _pointCloudData;
        private IOctree _spatialIndex;
        private volatile bool _disposed = false;

        public long MaxCacheMemory { get; set; }
        public long CurrentCacheMemory => _cache?.CurrentSize ?? 0;
        public int QualityLevel { get; set; } = 5;
        public string Name => "Point Cloud Streaming Manager";
        public long MemoryUsage => CurrentCacheMemory;

        public event EventHandler<StreamingUpdateEventArgs> StreamingUpdated;

        public StreamingManager() : this(new StreamingConfig())
        {
        }

        public StreamingManager(StreamingConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _cache = new StreamingCache(config.MaxCacheMemory);
            _activeChunks = new ConcurrentDictionary<string, StreamingChunk>();
            _loadingSemaphore = new SemaphoreSlim(config.LoadingThreads, config.LoadingThreads);
            MaxCacheMemory = config.MaxCacheMemory;

            // Register with memory manager
            MemoryManager.Instance.RegisterConsumer(this);
        }

        public async Task InitializeAsync(PointCloudData pointCloudData, IOctree spatialIndex)
        {
            _pointCloudData = pointCloudData ?? throw new ArgumentNullException(nameof(pointCloudData));
            _spatialIndex = spatialIndex ?? throw new ArgumentNullException(nameof(spatialIndex));

            await Task.Run(() =>
            {
                // Pre-create streaming chunks based on octree structure
                CreateStreamingChunks();
            });
        }

        public async Task UpdateStreamingAsync(BoundingBoxXYZ viewFrustum, XYZ cameraPosition, int levelOfDetail, CancellationToken cancellationToken = default)
        {
            if (_disposed || viewFrustum == null || cameraPosition == null)
                return;

            try
            {
                // Expand frustum for preloading
                var expandedFrustum = ExpandBounds(viewFrustum, _config.FrustumExpansionFactor);

                // Get required chunks for current view
                var requiredChunks = GetRequiredChunks(expandedFrustum, levelOfDetail);

                // Start loading tasks for missing chunks
                var loadingTasks = new List<Task>();
                long pointsToLoad = 0;

                foreach (var chunkId in requiredChunks)
                {
                    if (_activeChunks.TryGetValue(chunkId, out var chunk))
                    {
                        if (chunk.State == StreamingChunkState.NotLoaded)
                        {
                            pointsToLoad += chunk.PointCount;
                            loadingTasks.Add(LoadChunkAsync(chunk, cancellationToken));
                        }
                    }
                }

                // Unload chunks that are no longer needed
                await UnloadUnnecessaryChunks(expandedFrustum, levelOfDetail);

                // Wait for loading to complete (with timeout)
                if (loadingTasks.Count > 0)
                {
                    OnStreamingUpdated(new StreamingUpdateEventArgs
                    {
                        PointsLoaded = 0,
                        TotalPointsInView = pointsToLoad,
                        LoadingProgress = 0,
                        StatusMessage = $"Loading {loadingTasks.Count} chunks..."
                    });

                    await Task.WhenAll(loadingTasks);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in streaming update: {ex.Message}");
            }
        }

        public IEnumerable<PointCloudPoint> GetLoadedPoints()
        {
            return GetLoadedPoints(null);
        }

        public IEnumerable<PointCloudPoint> GetLoadedPoints(BoundingBoxXYZ bounds)
        {
            foreach (var chunk in _activeChunks.Values)
            {
                if (chunk.State == StreamingChunkState.Loaded && chunk.Points != null)
                {
                    if (bounds == null || chunk.Bounds.Intersects(bounds))
                    {
                        foreach (var point in chunk.Points)
                        {
                            if (bounds == null || IsPointInBounds(point.Position, bounds))
                            {
                                yield return point;
                            }
                        }
                    }
                }
            }
        }

        public async Task PreloadAsync(BoundingBoxXYZ bounds, StreamingPriority priority = StreamingPriority.Normal, CancellationToken cancellationToken = default)
        {
            if (bounds == null) return;

            var chunks = GetRequiredChunks(bounds, QualityLevel);
            var loadingTasks = new List<Task>();

            foreach (var chunkId in chunks)
            {
                if (_activeChunks.TryGetValue(chunkId, out var chunk) && 
                    chunk.State == StreamingChunkState.NotLoaded)
                {
                    loadingTasks.Add(LoadChunkAsync(chunk, cancellationToken));
                }
            }

            if (loadingTasks.Count > 0)
            {
                await Task.WhenAll(loadingTasks);
            }
        }

        public void ClearCache()
        {
            _cache?.Clear();
            
            foreach (var chunk in _activeChunks.Values)
            {
                chunk.State = StreamingChunkState.NotLoaded;
                chunk.Points?.Clear();
                chunk.CompressedData = null;
            }
        }

        public StreamingStatistics GetStatistics()
        {
            var cacheStats = _cache?.GetStatistics();
            
            return new StreamingStatistics
            {
                TotalPointsManaged = _pointCloudData?.PointCount ?? 0,
                PointsCurrentlyLoaded = _activeChunks.Values.Where(c => c.State == StreamingChunkState.Loaded).Sum(c => c.PointCount),
                CacheMemoryUsage = CurrentCacheMemory,
                MaxCacheMemory = MaxCacheMemory,
                CacheHitRatio = cacheStats?.HitRatio ?? 0,
                ActiveStreamingTasks = _loadingSemaphore.CurrentCount,
                AverageLoadTime = TimeSpan.FromMilliseconds(100), // Placeholder
                PointsLoadedPerSecond = 50000 // Placeholder
            };
        }

        public void ReleaseMemory()
        {
            // Release least recently used chunks
            var chunksToUnload = _activeChunks.Values
                .Where(c => c.State == StreamingChunkState.Loaded)
                .OrderBy(c => c.LastAccessed)
                .Take(_activeChunks.Count / 2)
                .ToList();

            foreach (var chunk in chunksToUnload)
            {
                UnloadChunk(chunk);
            }

            // Force cache cleanup
            _cache?.Clear();
        }

        #region Private Methods

        private void CreateStreamingChunks()
        {
            if (_spatialIndex?.Root == null) return;

            // Create chunks based on octree nodes
            var chunkId = 0;
            CreateChunksFromNode(_spatialIndex.Root, ref chunkId);
        }

        private void CreateChunksFromNode(IOctreeNode node, ref int chunkId)
        {
            var chunk = new StreamingChunk
            {
                Id = $"chunk_{chunkId++}",
                Bounds = node.Bounds,
                LevelOfDetail = node.Depth,
                PointCount = node.PointCount,
                State = StreamingChunkState.NotLoaded,
                LastAccessed = DateTime.MinValue
            };

            _activeChunks.TryAdd(chunk.Id, chunk);

            // Recursively create chunks for children
            if (node.Children != null)
            {
                foreach (var child in node.Children)
                {
                    CreateChunksFromNode(child, ref chunkId);
                }
            }
        }

        private List<string> GetRequiredChunks(BoundingBoxXYZ bounds, int levelOfDetail)
        {
            return _activeChunks.Values
                .Where(c => c.LevelOfDetail <= levelOfDetail && c.Bounds.Intersects(bounds))
                .Select(c => c.Id)
                .ToList();
        }

        private async Task LoadChunkAsync(StreamingChunk chunk, CancellationToken cancellationToken)
        {
            if (chunk.State != StreamingChunkState.NotLoaded) return;

            await _loadingSemaphore.WaitAsync(cancellationToken);
            
            try
            {
                chunk.State = StreamingChunkState.Loading;

                // Check cache first
                if (_cache.TryGet(chunk.Id, out var cachedChunk))
                {
                    chunk.Points = cachedChunk.Points;
                    chunk.CompressedData = cachedChunk.CompressedData;
                    chunk.State = StreamingChunkState.Loaded;
                    chunk.LastAccessed = DateTime.Now;
                    return;
                }

                // Load from spatial index
                var points = _spatialIndex.Query(chunk.Bounds).ToList();
                chunk.Points = points;
                chunk.State = StreamingChunkState.Loaded;
                chunk.LastAccessed = DateTime.Now;

                // Add to cache
                _cache.TryAdd(chunk.Id, chunk);
            }
            finally
            {
                _loadingSemaphore.Release();
            }
        }

        private async Task UnloadUnnecessaryChunks(BoundingBoxXYZ viewBounds, int levelOfDetail)
        {
            await Task.Run(() =>
            {
                var chunksToUnload = _activeChunks.Values
                    .Where(c => c.State == StreamingChunkState.Loaded && 
                               (!c.Bounds.Intersects(viewBounds) || c.LevelOfDetail > levelOfDetail))
                    .ToList();

                foreach (var chunk in chunksToUnload)
                {
                    UnloadChunk(chunk);
                }
            });
        }

        private void UnloadChunk(StreamingChunk chunk)
        {
            chunk.State = StreamingChunkState.NotLoaded;
            chunk.Points?.Clear();
            chunk.Points = null;
            chunk.CompressedData = null;
        }

        private BoundingBoxXYZ ExpandBounds(BoundingBoxXYZ bounds, double factor)
        {
            var center = new XYZ(
                (bounds.Min.X + bounds.Max.X) / 2,
                (bounds.Min.Y + bounds.Max.Y) / 2,
                (bounds.Min.Z + bounds.Max.Z) / 2);

            var size = new XYZ(
                (bounds.Max.X - bounds.Min.X) * factor / 2,
                (bounds.Max.Y - bounds.Min.Y) * factor / 2,
                (bounds.Max.Z - bounds.Min.Z) * factor / 2);

            return new BoundingBoxXYZ
            {
                Min = center.Subtract(size),
                Max = center.Add(size)
            };
        }

        private bool IsPointInBounds(XYZ point, BoundingBoxXYZ bounds)
        {
            return point.X >= bounds.Min.X && point.X <= bounds.Max.X &&
                   point.Y >= bounds.Min.Y && point.Y <= bounds.Max.Y &&
                   point.Z >= bounds.Min.Z && point.Z <= bounds.Max.Z;
        }

        protected virtual void OnStreamingUpdated(StreamingUpdateEventArgs e)
        {
            StreamingUpdated?.Invoke(this, e);
        }

        #endregion

        public void Dispose()
        {
            if (!_disposed)
            {
                MemoryManager.Instance.UnregisterConsumer(this);
                ClearCache();
                _loadingSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Simple implementation of streaming cache
    /// </summary>
    public class StreamingCache : IStreamingCache
    {
        private readonly ConcurrentDictionary<string, StreamingChunk> _cache;
        private readonly object _lock = new object();
        private long _currentSize;
        private long _totalRequests;
        private long _cacheHits;

        public long MaxSize { get; set; }
        public long CurrentSize => _currentSize;

        public StreamingCache(long maxSize)
        {
            MaxSize = maxSize;
            _cache = new ConcurrentDictionary<string, StreamingChunk>();
        }

        public bool TryAdd(string key, StreamingChunk chunk)
        {
            if (chunk.MemoryUsage + _currentSize > MaxSize)
            {
                // Evict least recently used items
                EvictLRU(chunk.MemoryUsage);
            }

            if (_cache.TryAdd(key, chunk))
            {
                Interlocked.Add(ref _currentSize, chunk.MemoryUsage);
                return true;
            }

            return false;
        }

        public bool TryGet(string key, out StreamingChunk chunk)
        {
            Interlocked.Increment(ref _totalRequests);
            
            if (_cache.TryGetValue(key, out chunk))
            {
                chunk.LastAccessed = DateTime.Now;
                Interlocked.Increment(ref _cacheHits);
                return true;
            }

            return false;
        }

        public bool TryRemove(string key)
        {
            if (_cache.TryRemove(key, out var chunk))
            {
                Interlocked.Add(ref _currentSize, -chunk.MemoryUsage);
                return true;
            }

            return false;
        }

        public void Clear()
        {
            _cache.Clear();
            Interlocked.Exchange(ref _currentSize, 0);
        }

        public CacheStatistics GetStatistics()
        {
            return new CacheStatistics
            {
                TotalRequests = _totalRequests,
                CacheHits = _cacheHits,
                CacheMisses = _totalRequests - _cacheHits,
                ChunkCount = _cache.Count,
                MemoryUsage = _currentSize,
                MaxMemory = MaxSize
            };
        }

        private void EvictLRU(long requiredSpace)
        {
            lock (_lock)
            {
                var itemsToEvict = _cache.Values
                    .OrderBy(c => c.LastAccessed)
                    .TakeWhile(c => 
                    {
                        requiredSpace -= c.MemoryUsage;
                        return requiredSpace > 0;
                    })
                    .ToList();

                foreach (var item in itemsToEvict)
                {
                    TryRemove(item.Id);
                }
            }
        }
    }
}
