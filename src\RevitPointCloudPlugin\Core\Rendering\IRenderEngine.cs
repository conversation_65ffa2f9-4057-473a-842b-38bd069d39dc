using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitPointCloudPlugin.Core.DataIngestion;

namespace RevitPointCloudPlugin.Core.Rendering
{
    /// <summary>
    /// Interface for point cloud rendering engine
    /// Handles high-performance rendering of point clouds in Revit viewports
    /// </summary>
    public interface IRenderEngine
    {
        /// <summary>
        /// Gets or sets the rendering quality level (0-10)
        /// </summary>
        int QualityLevel { get; set; }

        /// <summary>
        /// Gets or sets the point size for rendering
        /// </summary>
        float PointSize { get; set; }

        /// <summary>
        /// Gets or sets the color mode for point rendering
        /// </summary>
        PointColorMode ColorMode { get; set; }

        /// <summary>
        /// Gets or sets whether the point cloud is visible
        /// </summary>
        bool IsVisible { get; set; }

        /// <summary>
        /// Gets rendering statistics
        /// </summary>
        RenderingStatistics Statistics { get; }

        /// <summary>
        /// Event fired when rendering parameters change
        /// </summary>
        event EventHandler<RenderingChangedEventArgs> RenderingChanged;

        /// <summary>
        /// Initializes the rendering engine with Revit context
        /// </summary>
        /// <param name="uiApplication">Revit UI application</param>
        void Initialize(UIApplication uiApplication);

        /// <summary>
        /// Renders point cloud data in the specified view
        /// </summary>
        /// <param name="view">Revit view to render in</param>
        /// <param name="points">Points to render</param>
        /// <param name="transform">Transformation to apply</param>
        void Render(View view, IEnumerable<PointCloudPoint> points, Transform transform = null);

        /// <summary>
        /// Updates rendering for view changes
        /// </summary>
        /// <param name="view">Updated view</param>
        void UpdateView(View view);

        /// <summary>
        /// Clears all rendered content
        /// </summary>
        void Clear();

        /// <summary>
        /// Disposes rendering resources
        /// </summary>
        void Dispose();
    }

    /// <summary>
    /// Point cloud color modes
    /// </summary>
    public enum PointColorMode
    {
        RGB,
        Intensity,
        Classification,
        Elevation,
        ReturnNumber,
        Custom
    }

    /// <summary>
    /// Rendering performance statistics
    /// </summary>
    public class RenderingStatistics
    {
        public long PointsRendered { get; set; }
        public double FrameRate { get; set; }
        public TimeSpan RenderTime { get; set; }
        public long MemoryUsage { get; set; }
        public int DrawCalls { get; set; }
        public DateTime LastUpdate { get; set; }

        public override string ToString()
        {
            return $"Rendering: {PointsRendered:N0} points, {FrameRate:F1} FPS, {RenderTime.TotalMilliseconds:F1}ms";
        }
    }

    /// <summary>
    /// Event arguments for rendering changes
    /// </summary>
    public class RenderingChangedEventArgs : EventArgs
    {
        public PointColorMode ColorMode { get; set; }
        public float PointSize { get; set; }
        public bool VisibilityChanged { get; set; }
        public string ChangeReason { get; set; }
    }

    /// <summary>
    /// Configuration for rendering behavior
    /// </summary>
    public class RenderingConfig
    {
        /// <summary>
        /// Default point size
        /// </summary>
        public float DefaultPointSize { get; set; } = 1.0f;

        /// <summary>
        /// Maximum points to render per frame
        /// </summary>
        public int MaxPointsPerFrame { get; set; } = 1000000;

        /// <summary>
        /// Enable GPU acceleration if available
        /// </summary>
        public bool EnableGPUAcceleration { get; set; } = true;

        /// <summary>
        /// Enable point size attenuation with distance
        /// </summary>
        public bool EnableDistanceAttenuation { get; set; } = true;

        /// <summary>
        /// Color classification mapping
        /// </summary>
        public Dictionary<byte, Color> ClassificationColors { get; set; } = new Dictionary<byte, Color>
        {
            { 0, new Color(128, 128, 128) }, // Unclassified
            { 1, new Color(255, 255, 255) }, // Unassigned
            { 2, new Color(139, 69, 19) },   // Ground
            { 3, new Color(0, 255, 0) },     // Low Vegetation
            { 4, new Color(0, 128, 0) },     // Medium Vegetation
            { 5, new Color(0, 100, 0) },     // High Vegetation
            { 6, new Color(255, 0, 0) },     // Building
            { 7, new Color(255, 255, 0) },   // Low Point
            { 8, new Color(255, 0, 255) },   // Model Key-point
            { 9, new Color(0, 0, 255) },     // Water
            { 10, new Color(128, 128, 255) }, // Rail
            { 11, new Color(255, 128, 0) },   // Road Surface
            { 12, new Color(128, 255, 128) }, // Overlap Points
            { 13, new Color(255, 255, 128) }, // Wire - Guard
            { 14, new Color(255, 128, 255) }, // Wire - Conductor
            { 15, new Color(128, 255, 255) }, // Transmission Tower
            { 16, new Color(192, 192, 192) }, // Wire-structure Connector
            { 17, new Color(255, 192, 192) }, // Bridge Deck
            { 18, new Color(192, 255, 192) }  // High Noise
        };

        /// <summary>
        /// Intensity color ramp (from min to max intensity)
        /// </summary>
        public Color[] IntensityColorRamp { get; set; } = new Color[]
        {
            new Color(0, 0, 128),     // Dark blue (low intensity)
            new Color(0, 0, 255),     // Blue
            new Color(0, 255, 255),   // Cyan
            new Color(0, 255, 0),     // Green
            new Color(255, 255, 0),   // Yellow
            new Color(255, 128, 0),   // Orange
            new Color(255, 0, 0)      // Red (high intensity)
        };

        /// <summary>
        /// Elevation color ramp (from min to max elevation)
        /// </summary>
        public Color[] ElevationColorRamp { get; set; } = new Color[]
        {
            new Color(0, 0, 255),     // Blue (low elevation)
            new Color(0, 255, 255),   // Cyan
            new Color(0, 255, 0),     // Green
            new Color(255, 255, 0),   // Yellow
            new Color(255, 128, 0),   // Orange
            new Color(255, 0, 0),     // Red (high elevation)
            new Color(255, 255, 255)  // White (highest)
        };
    }

    /// <summary>
    /// Utility methods for color calculations
    /// </summary>
    public static class ColorUtils
    {
        /// <summary>
        /// Gets color based on intensity value
        /// </summary>
        /// <param name="intensity">Intensity value (0-65535)</param>
        /// <param name="colorRamp">Color ramp to use</param>
        /// <returns>Interpolated color</returns>
        public static Color GetIntensityColor(float intensity, Color[] colorRamp)
        {
            if (colorRamp == null || colorRamp.Length == 0)
                return new Color(128, 128, 128);

            // Normalize intensity to 0-1 range
            float normalizedIntensity = Math.Max(0, Math.Min(1, intensity / 65535f));
            
            // Find position in color ramp
            float position = normalizedIntensity * (colorRamp.Length - 1);
            int index = (int)Math.Floor(position);
            float fraction = position - index;

            if (index >= colorRamp.Length - 1)
                return colorRamp[colorRamp.Length - 1];

            // Interpolate between two colors
            var color1 = colorRamp[index];
            var color2 = colorRamp[index + 1];

            return new Color(
                (byte)(color1.Red + (color2.Red - color1.Red) * fraction),
                (byte)(color1.Green + (color2.Green - color1.Green) * fraction),
                (byte)(color1.Blue + (color2.Blue - color1.Blue) * fraction)
            );
        }

        /// <summary>
        /// Gets color based on elevation value
        /// </summary>
        /// <param name="elevation">Elevation value</param>
        /// <param name="minElevation">Minimum elevation in dataset</param>
        /// <param name="maxElevation">Maximum elevation in dataset</param>
        /// <param name="colorRamp">Color ramp to use</param>
        /// <returns>Interpolated color</returns>
        public static Color GetElevationColor(double elevation, double minElevation, double maxElevation, Color[] colorRamp)
        {
            if (colorRamp == null || colorRamp.Length == 0)
                return new Color(128, 128, 128);

            if (Math.Abs(maxElevation - minElevation) < 1e-6)
                return colorRamp[0];

            // Normalize elevation to 0-1 range
            float normalizedElevation = (float)Math.Max(0, Math.Min(1, (elevation - minElevation) / (maxElevation - minElevation)));
            
            // Find position in color ramp
            float position = normalizedElevation * (colorRamp.Length - 1);
            int index = (int)Math.Floor(position);
            float fraction = position - index;

            if (index >= colorRamp.Length - 1)
                return colorRamp[colorRamp.Length - 1];

            // Interpolate between two colors
            var color1 = colorRamp[index];
            var color2 = colorRamp[index + 1];

            return new Color(
                (byte)(color1.Red + (color2.Red - color1.Red) * fraction),
                (byte)(color1.Green + (color2.Green - color1.Green) * fraction),
                (byte)(color1.Blue + (color2.Blue - color1.Blue) * fraction)
            );
        }

        /// <summary>
        /// Gets color based on classification
        /// </summary>
        /// <param name="classification">Classification code</param>
        /// <param name="classificationColors">Classification color mapping</param>
        /// <returns>Classification color</returns>
        public static Color GetClassificationColor(byte classification, Dictionary<byte, Color> classificationColors)
        {
            if (classificationColors != null && classificationColors.TryGetValue(classification, out Color color))
                return color;

            // Default color for unknown classifications
            return new Color(128, 128, 128);
        }
    }
}
