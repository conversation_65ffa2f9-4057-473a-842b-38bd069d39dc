using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;

namespace RevitPointCloudPlugin.Core.DataIngestion
{
    /// <summary>
    /// Represents a single point in the point cloud with all associated data
    /// </summary>
    public struct PointCloudPoint
    {
        public XYZ Position { get; set; }
        public Color Color { get; set; }
        public float Intensity { get; set; }
        public byte Classification { get; set; }
        public DateTime TimeStamp { get; set; }
        public ushort ReturnNumber { get; set; }
        public ushort NumberOfReturns { get; set; }
    }

    /// <summary>
    /// Represents metadata for a point cloud dataset
    /// </summary>
    public class PointCloudMetadata
    {
        public string FileName { get; set; }
        public string Format { get; set; }
        public long TotalPoints { get; set; }
        public BoundingBoxXYZ BoundingBox { get; set; }
        public XYZ Scale { get; set; }
        public XYZ Offset { get; set; }
        public string CoordinateSystem { get; set; }
        public DateTime CreationDate { get; set; }
        public string CreatedBy { get; set; }
        public Dictionary<string, object> CustomProperties { get; set; }

        public PointCloudMetadata()
        {
            CustomProperties = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Main container for point cloud data with streaming capabilities
    /// </summary>
    public class PointCloudData
    {
        private readonly List<PointCloudPoint> _points;
        private readonly PointCloudMetadata _metadata;
        private bool _isLoaded;

        public PointCloudMetadata Metadata => _metadata;
        public bool IsLoaded => _isLoaded;
        public long PointCount => _points?.Count ?? 0;

        public PointCloudData(PointCloudMetadata metadata)
        {
            _metadata = metadata ?? throw new ArgumentNullException(nameof(metadata));
            _points = new List<PointCloudPoint>();
            _isLoaded = false;
        }

        /// <summary>
        /// Adds a batch of points to the dataset
        /// </summary>
        public void AddPoints(IEnumerable<PointCloudPoint> points)
        {
            if (points == null) throw new ArgumentNullException(nameof(points));
            _points.AddRange(points);
        }

        /// <summary>
        /// Gets points within a specified bounding box
        /// </summary>
        public IEnumerable<PointCloudPoint> GetPointsInBounds(BoundingBoxXYZ bounds)
        {
            if (bounds == null) throw new ArgumentNullException(nameof(bounds));

            foreach (var point in _points)
            {
                if (IsPointInBounds(point.Position, bounds))
                {
                    yield return point;
                }
            }
        }

        /// <summary>
        /// Gets all points (use with caution for large datasets)
        /// </summary>
        public IEnumerable<PointCloudPoint> GetAllPoints()
        {
            return _points;
        }

        /// <summary>
        /// Marks the dataset as fully loaded
        /// </summary>
        public void SetLoaded()
        {
            _isLoaded = true;
        }

        /// <summary>
        /// Clears all point data to free memory
        /// </summary>
        public void ClearPoints()
        {
            _points.Clear();
            _isLoaded = false;
        }

        private bool IsPointInBounds(XYZ point, BoundingBoxXYZ bounds)
        {
            return point.X >= bounds.Min.X && point.X <= bounds.Max.X &&
                   point.Y >= bounds.Min.Y && point.Y <= bounds.Max.Y &&
                   point.Z >= bounds.Min.Z && point.Z <= bounds.Max.Z;
        }
    }

    /// <summary>
    /// Event arguments for point cloud loading progress
    /// </summary>
    public class PointCloudLoadProgressEventArgs : EventArgs
    {
        public long PointsLoaded { get; set; }
        public long TotalPoints { get; set; }
        public double ProgressPercentage => TotalPoints > 0 ? (double)PointsLoaded / TotalPoints * 100 : 0;
        public string StatusMessage { get; set; }
    }
}
