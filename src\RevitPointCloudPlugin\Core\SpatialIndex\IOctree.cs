using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using RevitPointCloudPlugin.Core.DataIngestion;

namespace RevitPointCloudPlugin.Core.SpatialIndex
{
    /// <summary>
    /// Interface for octree spatial indexing structure
    /// Provides efficient spatial queries and level-of-detail management
    /// </summary>
    public interface IOctree
    {
        /// <summary>
        /// Gets the root bounding box of the octree
        /// </summary>
        BoundingBoxXYZ Bounds { get; }

        /// <summary>
        /// Gets the maximum depth of the octree
        /// </summary>
        int MaxDepth { get; }

        /// <summary>
        /// Gets the total number of points indexed
        /// </summary>
        long TotalPoints { get; }

        /// <summary>
        /// Gets the root node of the octree
        /// </summary>
        IOctreeNode Root { get; }

        /// <summary>
        /// Builds the octree from a collection of points
        /// </summary>
        /// <param name="points">Points to index</param>
        /// <param name="maxPointsPerNode">Maximum points per leaf node</param>
        /// <param name="maxDepth">Maximum tree depth</param>
        void Build(IEnumerable<PointCloudPoint> points, int maxPointsPerNode = 1000, int maxDepth = 10);

        /// <summary>
        /// Queries points within a bounding box
        /// </summary>
        /// <param name="queryBounds">Bounding box to query</param>
        /// <returns>Points within the bounds</returns>
        IEnumerable<PointCloudPoint> Query(BoundingBoxXYZ queryBounds);

        /// <summary>
        /// Queries points within a bounding box at a specific level of detail
        /// </summary>
        /// <param name="queryBounds">Bounding box to query</param>
        /// <param name="levelOfDetail">Level of detail (0 = highest detail)</param>
        /// <returns>Points within the bounds at specified LOD</returns>
        IEnumerable<PointCloudPoint> QueryLOD(BoundingBoxXYZ queryBounds, int levelOfDetail);

        /// <summary>
        /// Gets nodes that intersect with the query bounds
        /// </summary>
        /// <param name="queryBounds">Bounding box to query</param>
        /// <param name="maxDepth">Maximum depth to traverse</param>
        /// <returns>Intersecting nodes</returns>
        IEnumerable<IOctreeNode> GetIntersectingNodes(BoundingBoxXYZ queryBounds, int maxDepth = int.MaxValue);

        /// <summary>
        /// Serializes the octree to a stream for persistent storage
        /// </summary>
        /// <param name="stream">Stream to write to</param>
        void Serialize(System.IO.Stream stream);

        /// <summary>
        /// Deserializes the octree from a stream
        /// </summary>
        /// <param name="stream">Stream to read from</param>
        void Deserialize(System.IO.Stream stream);

        /// <summary>
        /// Clears all data from the octree
        /// </summary>
        void Clear();
    }

    /// <summary>
    /// Interface for octree nodes
    /// </summary>
    public interface IOctreeNode
    {
        /// <summary>
        /// Gets the bounding box of this node
        /// </summary>
        BoundingBoxXYZ Bounds { get; }

        /// <summary>
        /// Gets the depth level of this node (0 = root)
        /// </summary>
        int Depth { get; }

        /// <summary>
        /// Gets whether this node is a leaf node
        /// </summary>
        bool IsLeaf { get; }

        /// <summary>
        /// Gets the points stored in this node (only for leaf nodes)
        /// </summary>
        IReadOnlyList<PointCloudPoint> Points { get; }

        /// <summary>
        /// Gets the child nodes (null for leaf nodes)
        /// </summary>
        IOctreeNode[] Children { get; }

        /// <summary>
        /// Gets the number of points in this node and all descendants
        /// </summary>
        long PointCount { get; }

        /// <summary>
        /// Gets the center point of this node's bounds
        /// </summary>
        XYZ Center { get; }

        /// <summary>
        /// Gets the size (extent) of this node
        /// </summary>
        double Size { get; }

        /// <summary>
        /// Checks if this node intersects with the given bounds
        /// </summary>
        /// <param name="bounds">Bounds to test intersection</param>
        /// <returns>True if intersects</returns>
        bool Intersects(BoundingBoxXYZ bounds);

        /// <summary>
        /// Checks if this node is completely contained within the given bounds
        /// </summary>
        /// <param name="bounds">Bounds to test containment</param>
        /// <returns>True if completely contained</returns>
        bool IsContainedBy(BoundingBoxXYZ bounds);

        /// <summary>
        /// Gets a representative point for this node (for LOD rendering)
        /// </summary>
        /// <returns>Representative point</returns>
        PointCloudPoint GetRepresentativePoint();

        /// <summary>
        /// Gets multiple representative points for this node based on density
        /// </summary>
        /// <param name="maxPoints">Maximum number of points to return</param>
        /// <returns>Representative points</returns>
        IEnumerable<PointCloudPoint> GetRepresentativePoints(int maxPoints);
    }

    /// <summary>
    /// Event arguments for octree build progress
    /// </summary>
    public class OctreeBuildProgressEventArgs : EventArgs
    {
        public long ProcessedPoints { get; set; }
        public long TotalPoints { get; set; }
        public int CurrentDepth { get; set; }
        public int NodesCreated { get; set; }
        public double ProgressPercentage => TotalPoints > 0 ? (double)ProcessedPoints / TotalPoints * 100 : 0;
        public string StatusMessage { get; set; }
    }

    /// <summary>
    /// Configuration for octree building
    /// </summary>
    public class OctreeConfig
    {
        /// <summary>
        /// Maximum points per leaf node before subdivision
        /// </summary>
        public int MaxPointsPerNode { get; set; } = 1000;

        /// <summary>
        /// Maximum depth of the octree
        /// </summary>
        public int MaxDepth { get; set; } = 10;

        /// <summary>
        /// Minimum node size to prevent over-subdivision
        /// </summary>
        public double MinNodeSize { get; set; } = 0.01;

        /// <summary>
        /// Whether to store points in internal nodes for LOD
        /// </summary>
        public bool StoreLODPoints { get; set; } = true;

        /// <summary>
        /// Number of representative points to store per internal node
        /// </summary>
        public int LODPointsPerNode { get; set; } = 10;

        /// <summary>
        /// Whether to enable memory optimization (trade speed for memory)
        /// </summary>
        public bool EnableMemoryOptimization { get; set; } = true;
    }
}
