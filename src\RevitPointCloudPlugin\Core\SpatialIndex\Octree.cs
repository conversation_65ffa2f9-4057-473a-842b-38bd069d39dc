using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autodesk.Revit.DB;
using RevitPointCloudPlugin.Core.DataIngestion;

namespace RevitPointCloudPlugin.Core.SpatialIndex
{
    /// <summary>
    /// Implementation of an octree spatial index for efficient point cloud queries
    /// </summary>
    public class Octree : IOctree
    {
        private OctreeNode _root;
        private OctreeConfig _config;

        public BoundingBoxXYZ Bounds { get; private set; }
        public int MaxDepth => _config?.MaxDepth ?? 0;
        public long TotalPoints { get; private set; }
        public IOctreeNode Root => _root;

        /// <summary>
        /// Event fired during octree building to report progress
        /// </summary>
        public event EventHandler<OctreeBuildProgressEventArgs> BuildProgressChanged;

        /// <summary>
        /// Creates a new octree with default configuration
        /// </summary>
        public Octree() : this(new OctreeConfig())
        {
        }

        /// <summary>
        /// Creates a new octree with specified configuration
        /// </summary>
        public Octree(OctreeConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// Builds the octree from a collection of points
        /// </summary>
        public void Build(IEnumerable<PointCloudPoint> points, int maxPointsPerNode = 1000, int maxDepth = 10)
        {
            if (points == null) throw new ArgumentNullException(nameof(points));

            // Update configuration
            _config.MaxPointsPerNode = maxPointsPerNode;
            _config.MaxDepth = maxDepth;

            // Convert to list for multiple passes
            var pointList = points.ToList();
            if (pointList.Count == 0)
            {
                throw new ArgumentException("Point collection cannot be empty", nameof(points));
            }

            TotalPoints = pointList.Count;

            // Calculate bounding box
            Bounds = CalculateBoundingBox(pointList);

            // Create root node
            _root = new OctreeNode(Bounds, 0, _config);

            // Add points with progress reporting
            long processedPoints = 0;
            const int batchSize = 10000;

            OnBuildProgressChanged(new OctreeBuildProgressEventArgs
            {
                ProcessedPoints = 0,
                TotalPoints = TotalPoints,
                CurrentDepth = 0,
                NodesCreated = 1,
                StatusMessage = "Starting octree construction..."
            });

            for (int i = 0; i < pointList.Count; i += batchSize)
            {
                var batch = pointList.Skip(i).Take(batchSize);
                _root.AddPoints(batch);
                
                processedPoints += batch.Count();

                OnBuildProgressChanged(new OctreeBuildProgressEventArgs
                {
                    ProcessedPoints = processedPoints,
                    TotalPoints = TotalPoints,
                    CurrentDepth = GetCurrentMaxDepth(),
                    NodesCreated = CountNodes(),
                    StatusMessage = $"Building octree: {processedPoints:N0} / {TotalPoints:N0} points processed"
                });
            }

            OnBuildProgressChanged(new OctreeBuildProgressEventArgs
            {
                ProcessedPoints = TotalPoints,
                TotalPoints = TotalPoints,
                CurrentDepth = GetCurrentMaxDepth(),
                NodesCreated = CountNodes(),
                StatusMessage = "Octree construction completed"
            });
        }

        /// <summary>
        /// Queries points within a bounding box
        /// </summary>
        public IEnumerable<PointCloudPoint> Query(BoundingBoxXYZ queryBounds)
        {
            if (_root == null || queryBounds == null)
                return Enumerable.Empty<PointCloudPoint>();

            return _root.Query(queryBounds);
        }

        /// <summary>
        /// Queries points within a bounding box at a specific level of detail
        /// </summary>
        public IEnumerable<PointCloudPoint> QueryLOD(BoundingBoxXYZ queryBounds, int levelOfDetail)
        {
            if (_root == null || queryBounds == null)
                return Enumerable.Empty<PointCloudPoint>();

            return QueryLODRecursive(_root, queryBounds, levelOfDetail, 0);
        }

        /// <summary>
        /// Gets nodes that intersect with the query bounds
        /// </summary>
        public IEnumerable<IOctreeNode> GetIntersectingNodes(BoundingBoxXYZ queryBounds, int maxDepth = int.MaxValue)
        {
            if (_root == null || queryBounds == null)
                return Enumerable.Empty<IOctreeNode>();

            return GetIntersectingNodesRecursive(_root, queryBounds, maxDepth);
        }

        /// <summary>
        /// Serializes the octree to a stream
        /// </summary>
        public void Serialize(Stream stream)
        {
            if (stream == null) throw new ArgumentNullException(nameof(stream));

            using (var writer = new BinaryWriter(stream, System.Text.Encoding.UTF8, true))
            {
                // Write header
                writer.Write("OCTREE_V1"); // Magic number and version
                writer.Write(TotalPoints);
                writer.Write(_config.MaxPointsPerNode);
                writer.Write(_config.MaxDepth);

                // Write bounds
                WriteBounds(writer, Bounds);

                // Write tree structure
                if (_root != null)
                {
                    writer.Write(true); // Has root
                    SerializeNode(writer, _root);
                }
                else
                {
                    writer.Write(false); // No root
                }
            }
        }

        /// <summary>
        /// Deserializes the octree from a stream
        /// </summary>
        public void Deserialize(Stream stream)
        {
            if (stream == null) throw new ArgumentNullException(nameof(stream));

            using (var reader = new BinaryReader(stream, System.Text.Encoding.UTF8, true))
            {
                // Read header
                var magic = reader.ReadString();
                if (magic != "OCTREE_V1")
                    throw new InvalidDataException("Invalid octree file format");

                TotalPoints = reader.ReadInt64();
                _config.MaxPointsPerNode = reader.ReadInt32();
                _config.MaxDepth = reader.ReadInt32();

                // Read bounds
                Bounds = ReadBounds(reader);

                // Read tree structure
                bool hasRoot = reader.ReadBoolean();
                if (hasRoot)
                {
                    _root = DeserializeNode(reader, 0);
                }
                else
                {
                    _root = null;
                }
            }
        }

        /// <summary>
        /// Clears all data from the octree
        /// </summary>
        public void Clear()
        {
            _root = null;
            Bounds = null;
            TotalPoints = 0;
        }

        #region Private Helper Methods

        private BoundingBoxXYZ CalculateBoundingBox(IList<PointCloudPoint> points)
        {
            if (points.Count == 0)
                throw new ArgumentException("Cannot calculate bounding box for empty point collection");

            var first = points[0].Position;
            double minX = first.X, maxX = first.X;
            double minY = first.Y, maxY = first.Y;
            double minZ = first.Z, maxZ = first.Z;

            foreach (var point in points)
            {
                var pos = point.Position;
                if (pos.X < minX) minX = pos.X;
                if (pos.X > maxX) maxX = pos.X;
                if (pos.Y < minY) minY = pos.Y;
                if (pos.Y > maxY) maxY = pos.Y;
                if (pos.Z < minZ) minZ = pos.Z;
                if (pos.Z > maxZ) maxZ = pos.Z;
            }

            // Add small padding to ensure all points are contained
            const double padding = 0.001;
            return new BoundingBoxXYZ
            {
                Min = new XYZ(minX - padding, minY - padding, minZ - padding),
                Max = new XYZ(maxX + padding, maxY + padding, maxZ + padding)
            };
        }

        private IEnumerable<PointCloudPoint> QueryLODRecursive(IOctreeNode node, BoundingBoxXYZ queryBounds, int targetLOD, int currentDepth)
        {
            if (!node.Intersects(queryBounds))
                yield break;

            if (currentDepth >= targetLOD || node.IsLeaf)
            {
                // Return representative points for this level
                foreach (var point in node.GetRepresentativePoints(Math.Max(1, _config.LODPointsPerNode)))
                {
                    if (IsPointInBounds(point.Position, queryBounds))
                    {
                        yield return point;
                    }
                }
            }
            else if (node.Children != null)
            {
                foreach (var child in node.Children)
                {
                    foreach (var point in QueryLODRecursive(child, queryBounds, targetLOD, currentDepth + 1))
                    {
                        yield return point;
                    }
                }
            }
        }

        private IEnumerable<IOctreeNode> GetIntersectingNodesRecursive(IOctreeNode node, BoundingBoxXYZ queryBounds, int maxDepth)
        {
            if (!node.Intersects(queryBounds) || node.Depth > maxDepth)
                yield break;

            yield return node;

            if (!node.IsLeaf && node.Children != null)
            {
                foreach (var child in node.Children)
                {
                    foreach (var intersectingNode in GetIntersectingNodesRecursive(child, queryBounds, maxDepth))
                    {
                        yield return intersectingNode;
                    }
                }
            }
        }

        private bool IsPointInBounds(XYZ point, BoundingBoxXYZ bounds)
        {
            return point.X >= bounds.Min.X && point.X <= bounds.Max.X &&
                   point.Y >= bounds.Min.Y && point.Y <= bounds.Max.Y &&
                   point.Z >= bounds.Min.Z && point.Z <= bounds.Max.Z;
        }

        private int GetCurrentMaxDepth()
        {
            return GetMaxDepthRecursive(_root);
        }

        private int GetMaxDepthRecursive(IOctreeNode node)
        {
            if (node == null || node.IsLeaf)
                return node?.Depth ?? 0;

            int maxChildDepth = 0;
            if (node.Children != null)
            {
                foreach (var child in node.Children)
                {
                    maxChildDepth = Math.Max(maxChildDepth, GetMaxDepthRecursive(child));
                }
            }

            return maxChildDepth;
        }

        private int CountNodes()
        {
            return CountNodesRecursive(_root);
        }

        private int CountNodesRecursive(IOctreeNode node)
        {
            if (node == null) return 0;

            int count = 1;
            if (node.Children != null)
            {
                foreach (var child in node.Children)
                {
                    count += CountNodesRecursive(child);
                }
            }

            return count;
        }

        private void WriteBounds(BinaryWriter writer, BoundingBoxXYZ bounds)
        {
            writer.Write(bounds.Min.X);
            writer.Write(bounds.Min.Y);
            writer.Write(bounds.Min.Z);
            writer.Write(bounds.Max.X);
            writer.Write(bounds.Max.Y);
            writer.Write(bounds.Max.Z);
        }

        private BoundingBoxXYZ ReadBounds(BinaryReader reader)
        {
            return new BoundingBoxXYZ
            {
                Min = new XYZ(reader.ReadDouble(), reader.ReadDouble(), reader.ReadDouble()),
                Max = new XYZ(reader.ReadDouble(), reader.ReadDouble(), reader.ReadDouble())
            };
        }

        private void SerializeNode(BinaryWriter writer, IOctreeNode node)
        {
            // TODO: Implement node serialization
            throw new NotImplementedException("Node serialization not yet implemented");
        }

        private OctreeNode DeserializeNode(BinaryReader reader, int depth)
        {
            // TODO: Implement node deserialization
            throw new NotImplementedException("Node deserialization not yet implemented");
        }

        protected virtual void OnBuildProgressChanged(OctreeBuildProgressEventArgs e)
        {
            BuildProgressChanged?.Invoke(this, e);
        }

        #endregion
    }
}
