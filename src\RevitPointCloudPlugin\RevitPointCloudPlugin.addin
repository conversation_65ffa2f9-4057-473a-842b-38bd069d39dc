<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>Revit Point Cloud Plugin</Name>
    <Assembly>RevitPointCloudPlugin.dll</Assembly>
    <FullClassName>RevitPointCloudPlugin.PluginApplication</FullClassName>
    <ClientId>A1B2C3D4-E5F6-7890-ABCD-EF1234567890</ClientId>
    <VendorId>CloudPointForRevit</VendorId>
    <VendorDescription>Advanced Point Cloud Visualization for Revit</VendorDescription>
    <Description>Plugin for visualizing and interacting with massive point clouds directly in Revit using out-of-core streaming technology. Supports .las and .e57 formats without dependency on Autodesk Reality Solutions SDK.</Description>
    <VisibilityMode>AlwaysVisible</VisibilityMode>
  </AddIn>
</RevitAddIns>
