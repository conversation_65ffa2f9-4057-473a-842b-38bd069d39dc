using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using RevitPointCloudPlugin.Core.DataIngestion;

namespace RevitPointCloudPlugin.Core.SpatialIndex
{
    /// <summary>
    /// Implementation of an octree node for spatial indexing
    /// </summary>
    public class OctreeNode : IOctreeNode
    {
        private readonly List<PointCloudPoint> _points;
        private readonly List<PointCloudPoint> _lodPoints;
        private OctreeNode[] _children;
        private readonly OctreeConfig _config;

        public BoundingBoxXYZ Bounds { get; private set; }
        public int Depth { get; private set; }
        public bool IsLeaf => _children == null;
        public IReadOnlyList<PointCloudPoint> Points => _points?.AsReadOnly();
        public IOctreeNode[] Children => _children;
        public long PointCount { get; private set; }
        public XYZ Center { get; private set; }
        public double Size { get; private set; }

        /// <summary>
        /// Creates a new octree node
        /// </summary>
        public OctreeNode(BoundingBoxXYZ bounds, int depth, OctreeConfig config)
        {
            Bounds = bounds ?? throw new ArgumentNullException(nameof(bounds));
            Depth = depth;
            _config = config ?? throw new ArgumentNullException(nameof(config));
            
            _points = new List<PointCloudPoint>();
            if (_config.StoreLODPoints)
            {
                _lodPoints = new List<PointCloudPoint>();
            }

            Center = new XYZ(
                (bounds.Min.X + bounds.Max.X) / 2,
                (bounds.Min.Y + bounds.Max.Y) / 2,
                (bounds.Min.Z + bounds.Max.Z) / 2
            );

            Size = Math.Max(Math.Max(
                bounds.Max.X - bounds.Min.X,
                bounds.Max.Y - bounds.Min.Y),
                bounds.Max.Z - bounds.Min.Z);
        }

        /// <summary>
        /// Adds points to this node, subdividing if necessary
        /// </summary>
        public void AddPoints(IEnumerable<PointCloudPoint> points)
        {
            foreach (var point in points)
            {
                AddPoint(point);
            }
        }

        /// <summary>
        /// Adds a single point to this node
        /// </summary>
        public void AddPoint(PointCloudPoint point)
        {
            if (!ContainsPoint(point.Position))
                return;

            PointCount++;

            // If this is a leaf node and we haven't exceeded limits, add directly
            if (IsLeaf)
            {
                _points.Add(point);

                // Check if we need to subdivide
                if (_points.Count > _config.MaxPointsPerNode && 
                    Depth < _config.MaxDepth && 
                    Size > _config.MinNodeSize)
                {
                    Subdivide();
                }
            }
            else
            {
                // Add to appropriate child
                foreach (var child in _children)
                {
                    if (child.ContainsPoint(point.Position))
                    {
                        child.AddPoint(point);
                        break;
                    }
                }
            }

            // Store LOD points for internal nodes
            if (_config.StoreLODPoints && !IsLeaf && _lodPoints.Count < _config.LODPointsPerNode)
            {
                _lodPoints.Add(point);
            }
        }

        /// <summary>
        /// Subdivides this node into 8 children
        /// </summary>
        private void Subdivide()
        {
            if (!IsLeaf) return;

            _children = new OctreeNode[8];
            var center = Center;
            var min = Bounds.Min;
            var max = Bounds.Max;

            // Create 8 child nodes
            _children[0] = new OctreeNode(CreateBounds(min.X, center.X, min.Y, center.Y, min.Z, center.Z), Depth + 1, _config);
            _children[1] = new OctreeNode(CreateBounds(center.X, max.X, min.Y, center.Y, min.Z, center.Z), Depth + 1, _config);
            _children[2] = new OctreeNode(CreateBounds(min.X, center.X, center.Y, max.Y, min.Z, center.Z), Depth + 1, _config);
            _children[3] = new OctreeNode(CreateBounds(center.X, max.X, center.Y, max.Y, min.Z, center.Z), Depth + 1, _config);
            _children[4] = new OctreeNode(CreateBounds(min.X, center.X, min.Y, center.Y, center.Z, max.Z), Depth + 1, _config);
            _children[5] = new OctreeNode(CreateBounds(center.X, max.X, min.Y, center.Y, center.Z, max.Z), Depth + 1, _config);
            _children[6] = new OctreeNode(CreateBounds(min.X, center.X, center.Y, max.Y, center.Z, max.Z), Depth + 1, _config);
            _children[7] = new OctreeNode(CreateBounds(center.X, max.X, center.Y, max.Y, center.Z, max.Z), Depth + 1, _config);

            // Redistribute points to children
            var pointsToRedistribute = new List<PointCloudPoint>(_points);
            _points.Clear();

            foreach (var point in pointsToRedistribute)
            {
                foreach (var child in _children)
                {
                    if (child.ContainsPoint(point.Position))
                    {
                        child.AddPoint(point);
                        break;
                    }
                }
            }

            // Store some points for LOD if enabled
            if (_config.StoreLODPoints)
            {
                var lodCount = Math.Min(_config.LODPointsPerNode, pointsToRedistribute.Count);
                for (int i = 0; i < lodCount; i++)
                {
                    _lodPoints.Add(pointsToRedistribute[i * pointsToRedistribute.Count / lodCount]);
                }
            }
        }

        /// <summary>
        /// Creates a bounding box from coordinates
        /// </summary>
        private BoundingBoxXYZ CreateBounds(double minX, double maxX, double minY, double maxY, double minZ, double maxZ)
        {
            return new BoundingBoxXYZ
            {
                Min = new XYZ(minX, minY, minZ),
                Max = new XYZ(maxX, maxY, maxZ)
            };
        }

        /// <summary>
        /// Checks if a point is contained within this node's bounds
        /// </summary>
        private bool ContainsPoint(XYZ point)
        {
            return point.X >= Bounds.Min.X && point.X <= Bounds.Max.X &&
                   point.Y >= Bounds.Min.Y && point.Y <= Bounds.Max.Y &&
                   point.Z >= Bounds.Min.Z && point.Z <= Bounds.Max.Z;
        }

        /// <summary>
        /// Checks if this node intersects with the given bounds
        /// </summary>
        public bool Intersects(BoundingBoxXYZ bounds)
        {
            return !(bounds.Max.X < Bounds.Min.X || bounds.Min.X > Bounds.Max.X ||
                     bounds.Max.Y < Bounds.Min.Y || bounds.Min.Y > Bounds.Max.Y ||
                     bounds.Max.Z < Bounds.Min.Z || bounds.Min.Z > Bounds.Max.Z);
        }

        /// <summary>
        /// Checks if this node is completely contained within the given bounds
        /// </summary>
        public bool IsContainedBy(BoundingBoxXYZ bounds)
        {
            return bounds.Min.X <= Bounds.Min.X && bounds.Max.X >= Bounds.Max.X &&
                   bounds.Min.Y <= Bounds.Min.Y && bounds.Max.Y >= Bounds.Max.Y &&
                   bounds.Min.Z <= Bounds.Min.Z && bounds.Max.Z >= Bounds.Max.Z;
        }

        /// <summary>
        /// Gets a representative point for this node
        /// </summary>
        public PointCloudPoint GetRepresentativePoint()
        {
            if (IsLeaf && _points.Count > 0)
            {
                return _points[0];
            }
            else if (_lodPoints?.Count > 0)
            {
                return _lodPoints[0];
            }
            else if (!IsLeaf && _children != null)
            {
                foreach (var child in _children)
                {
                    if (child.PointCount > 0)
                    {
                        return child.GetRepresentativePoint();
                    }
                }
            }

            // Fallback: create a point at the center
            return new PointCloudPoint
            {
                Position = Center,
                Color = new Color(128, 128, 128),
                Intensity = 32767,
                Classification = 1
            };
        }

        /// <summary>
        /// Gets multiple representative points for this node
        /// </summary>
        public IEnumerable<PointCloudPoint> GetRepresentativePoints(int maxPoints)
        {
            if (IsLeaf)
            {
                return _points.Take(maxPoints);
            }
            else if (_lodPoints?.Count > 0)
            {
                return _lodPoints.Take(maxPoints);
            }
            else if (!IsLeaf && _children != null)
            {
                var result = new List<PointCloudPoint>();
                int pointsPerChild = maxPoints / _children.Length;
                
                foreach (var child in _children)
                {
                    if (child.PointCount > 0)
                    {
                        result.AddRange(child.GetRepresentativePoints(Math.Max(1, pointsPerChild)));
                        if (result.Count >= maxPoints) break;
                    }
                }
                
                return result.Take(maxPoints);
            }

            return new[] { GetRepresentativePoint() };
        }

        /// <summary>
        /// Queries points within the specified bounds
        /// </summary>
        public IEnumerable<PointCloudPoint> Query(BoundingBoxXYZ queryBounds)
        {
            if (!Intersects(queryBounds))
                yield break;

            if (IsLeaf)
            {
                foreach (var point in _points)
                {
                    if (IsPointInBounds(point.Position, queryBounds))
                    {
                        yield return point;
                    }
                }
            }
            else if (_children != null)
            {
                foreach (var child in _children)
                {
                    foreach (var point in child.Query(queryBounds))
                    {
                        yield return point;
                    }
                }
            }
        }

        private bool IsPointInBounds(XYZ point, BoundingBoxXYZ bounds)
        {
            return point.X >= bounds.Min.X && point.X <= bounds.Max.X &&
                   point.Y >= bounds.Min.Y && point.Y <= bounds.Max.Y &&
                   point.Z >= bounds.Min.Z && point.Z <= bounds.Max.Z;
        }
    }
}
