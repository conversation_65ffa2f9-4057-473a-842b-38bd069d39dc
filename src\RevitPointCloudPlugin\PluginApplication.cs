using System;
using System.Collections.Generic;
using System.Reflection;
using System.Windows.Media.Imaging;
using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using RevitPointCloudPlugin.UI.Ribbon;

namespace RevitPointCloudPlugin
{
    /// <summary>
    /// Main application class for the Revit Point Cloud Plugin
    /// Implements IExternalApplication to integrate with Revit's application lifecycle
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class PluginApplication : IExternalApplication
    {
        private static PluginApplication _instance;
        private PointCloudRibbon _ribbon;

        /// <summary>
        /// Gets the singleton instance of the plugin application
        /// </summary>
        public static PluginApplication Instance => _instance;

        /// <summary>
        /// Called when Revit starts up - initializes the plugin
        /// </summary>
        /// <param name="application">The Revit application object</param>
        /// <returns>Result indicating success or failure</returns>
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                _instance = this;
                
                // Initialize the ribbon UI
                _ribbon = new PointCloudRibbon();
                _ribbon.CreateRibbon(application);

                // Log successful startup
                TaskDialog.Show("Point Cloud Plugin", 
                    "Point Cloud Plugin loaded successfully!\n" +
                    "Version: " + Assembly.GetExecutingAssembly().GetName().Version);

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Point Cloud Plugin Error", 
                    "Failed to load Point Cloud Plugin:\n" + ex.Message);
                return Result.Failed;
            }
        }

        /// <summary>
        /// Called when Revit shuts down - cleanup resources
        /// </summary>
        /// <param name="application">The Revit application object</param>
        /// <returns>Result indicating success or failure</returns>
        public Result OnShutdown(UIControlledApplication application)
        {
            try
            {
                // Cleanup resources
                _ribbon?.Dispose();
                _ribbon = null;
                _instance = null;

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Point Cloud Plugin Error", 
                    "Error during plugin shutdown:\n" + ex.Message);
                return Result.Failed;
            }
        }

        /// <summary>
        /// Gets the assembly directory path
        /// </summary>
        public static string AssemblyDirectory
        {
            get
            {
                string codeBase = Assembly.GetExecutingAssembly().CodeBase;
                UriBuilder uri = new UriBuilder(codeBase);
                string path = Uri.UnescapeDataString(uri.Path);
                return System.IO.Path.GetDirectoryName(path);
            }
        }
    }
}
