using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime;
using System.Threading;
using System.Threading.Tasks;

namespace RevitPointCloudPlugin.Utils
{
    /// <summary>
    /// Memory management utility for point cloud operations
    /// Ensures memory usage stays within specified limits and provides cleanup mechanisms
    /// </summary>
    public class MemoryManager
    {
        private static MemoryManager _instance;
        private readonly object _lock = new object();
        private readonly Timer _memoryMonitorTimer;
        private readonly List<IMemoryConsumer> _consumers;
        private long _maxMemoryBytes;
        private bool _disposed = false;

        /// <summary>
        /// Gets the singleton instance of the memory manager
        /// </summary>
        public static MemoryManager Instance => _instance ??= new MemoryManager();

        /// <summary>
        /// Event fired when memory usage exceeds threshold
        /// </summary>
        public event EventHandler<MemoryPressureEventArgs> MemoryPressure;

        /// <summary>
        /// Gets or sets the maximum memory usage in bytes (default: 2GB)
        /// </summary>
        public long MaxMemoryBytes
        {
            get => _maxMemoryBytes;
            set => _maxMemoryBytes = Math.Max(value, 512 * 1024 * 1024); // Minimum 512MB
        }

        /// <summary>
        /// Gets the current memory usage in bytes
        /// </summary>
        public long CurrentMemoryUsage => GC.GetTotalMemory(false);

        /// <summary>
        /// Gets the memory usage percentage (0-100)
        /// </summary>
        public double MemoryUsagePercentage => (double)CurrentMemoryUsage / MaxMemoryBytes * 100;

        /// <summary>
        /// Gets whether memory usage is critical (>90%)
        /// </summary>
        public bool IsMemoryCritical => MemoryUsagePercentage > 90;

        private MemoryManager()
        {
            _consumers = new List<IMemoryConsumer>();
            _maxMemoryBytes = 2L * 1024 * 1024 * 1024; // 2GB default
            
            // Start memory monitoring timer (check every 5 seconds)
            _memoryMonitorTimer = new Timer(MonitorMemoryUsage, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
        }

        /// <summary>
        /// Registers a memory consumer for monitoring and cleanup
        /// </summary>
        /// <param name="consumer">Memory consumer to register</param>
        public void RegisterConsumer(IMemoryConsumer consumer)
        {
            if (consumer == null) throw new ArgumentNullException(nameof(consumer));

            lock (_lock)
            {
                if (!_consumers.Contains(consumer))
                {
                    _consumers.Add(consumer);
                }
            }
        }

        /// <summary>
        /// Unregisters a memory consumer
        /// </summary>
        /// <param name="consumer">Memory consumer to unregister</param>
        public void UnregisterConsumer(IMemoryConsumer consumer)
        {
            if (consumer == null) return;

            lock (_lock)
            {
                _consumers.Remove(consumer);
            }
        }

        /// <summary>
        /// Forces garbage collection and memory cleanup
        /// </summary>
        public void ForceCleanup()
        {
            // Cleanup registered consumers first
            lock (_lock)
            {
                foreach (var consumer in _consumers)
                {
                    try
                    {
                        consumer.ReleaseMemory();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error during memory cleanup: {ex.Message}");
                    }
                }
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            // Compact large object heap if available (.NET 4.5.1+)
            GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
            GC.Collect();
        }

        /// <summary>
        /// Checks if there's enough memory available for an operation
        /// </summary>
        /// <param name="requiredBytes">Required memory in bytes</param>
        /// <returns>True if memory is available</returns>
        public bool IsMemoryAvailable(long requiredBytes)
        {
            var availableMemory = MaxMemoryBytes - CurrentMemoryUsage;
            return availableMemory >= requiredBytes;
        }

        /// <summary>
        /// Attempts to free the specified amount of memory
        /// </summary>
        /// <param name="targetBytes">Target amount of memory to free</param>
        /// <returns>True if successful</returns>
        public bool TryFreeMemory(long targetBytes)
        {
            var initialMemory = CurrentMemoryUsage;
            
            // Try cleanup without forcing GC first
            lock (_lock)
            {
                var sortedConsumers = new List<IMemoryConsumer>(_consumers);
                sortedConsumers.Sort((a, b) => b.MemoryUsage.CompareTo(a.MemoryUsage));

                foreach (var consumer in sortedConsumers)
                {
                    try
                    {
                        var beforeCleanup = CurrentMemoryUsage;
                        consumer.ReleaseMemory();
                        var afterCleanup = CurrentMemoryUsage;
                        
                        var freedMemory = initialMemory - afterCleanup;
                        if (freedMemory >= targetBytes)
                        {
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error during targeted memory cleanup: {ex.Message}");
                    }
                }
            }

            // If not enough memory freed, force GC
            ForceCleanup();
            
            var finalMemory = CurrentMemoryUsage;
            var totalFreed = initialMemory - finalMemory;
            
            return totalFreed >= targetBytes;
        }

        /// <summary>
        /// Gets memory statistics
        /// </summary>
        /// <returns>Memory statistics</returns>
        public MemoryStatistics GetMemoryStatistics()
        {
            var stats = new MemoryStatistics
            {
                TotalMemoryUsage = CurrentMemoryUsage,
                MaxMemoryLimit = MaxMemoryBytes,
                UsagePercentage = MemoryUsagePercentage,
                AvailableMemory = MaxMemoryBytes - CurrentMemoryUsage,
                ConsumerCount = _consumers.Count
            };

            lock (_lock)
            {
                foreach (var consumer in _consumers)
                {
                    stats.ConsumerMemoryUsage.Add(consumer.Name, consumer.MemoryUsage);
                }
            }

            return stats;
        }

        /// <summary>
        /// Monitors memory usage and triggers cleanup if necessary
        /// </summary>
        private void MonitorMemoryUsage(object state)
        {
            try
            {
                var usagePercentage = MemoryUsagePercentage;
                
                if (usagePercentage > 80) // Warning threshold
                {
                    OnMemoryPressure(new MemoryPressureEventArgs
                    {
                        CurrentUsage = CurrentMemoryUsage,
                        MaxMemory = MaxMemoryBytes,
                        UsagePercentage = usagePercentage,
                        PressureLevel = usagePercentage > 90 ? MemoryPressureLevel.Critical : MemoryPressureLevel.High
                    });

                    if (usagePercentage > 90) // Critical threshold
                    {
                        // Attempt automatic cleanup
                        Task.Run(() => TryFreeMemory(MaxMemoryBytes / 10)); // Try to free 10% of max memory
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in memory monitoring: {ex.Message}");
            }
        }

        /// <summary>
        /// Raises the MemoryPressure event
        /// </summary>
        protected virtual void OnMemoryPressure(MemoryPressureEventArgs e)
        {
            MemoryPressure?.Invoke(this, e);
        }

        /// <summary>
        /// Disposes the memory manager
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _memoryMonitorTimer?.Dispose();
                
                lock (_lock)
                {
                    _consumers.Clear();
                }
                
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Interface for objects that consume significant memory and can release it on demand
    /// </summary>
    public interface IMemoryConsumer
    {
        /// <summary>
        /// Gets the name of this memory consumer
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the current memory usage in bytes
        /// </summary>
        long MemoryUsage { get; }

        /// <summary>
        /// Releases memory that can be freed
        /// </summary>
        void ReleaseMemory();
    }

    /// <summary>
    /// Event arguments for memory pressure events
    /// </summary>
    public class MemoryPressureEventArgs : EventArgs
    {
        public long CurrentUsage { get; set; }
        public long MaxMemory { get; set; }
        public double UsagePercentage { get; set; }
        public MemoryPressureLevel PressureLevel { get; set; }
    }

    /// <summary>
    /// Memory pressure levels
    /// </summary>
    public enum MemoryPressureLevel
    {
        Normal,
        High,
        Critical
    }

    /// <summary>
    /// Memory usage statistics
    /// </summary>
    public class MemoryStatistics
    {
        public long TotalMemoryUsage { get; set; }
        public long MaxMemoryLimit { get; set; }
        public double UsagePercentage { get; set; }
        public long AvailableMemory { get; set; }
        public int ConsumerCount { get; set; }
        public Dictionary<string, long> ConsumerMemoryUsage { get; set; } = new Dictionary<string, long>();

        public override string ToString()
        {
            return $"Memory Usage: {UsagePercentage:F1}% ({TotalMemoryUsage / (1024 * 1024):N0} MB / {MaxMemoryLimit / (1024 * 1024):N0} MB)";
        }
    }
}
