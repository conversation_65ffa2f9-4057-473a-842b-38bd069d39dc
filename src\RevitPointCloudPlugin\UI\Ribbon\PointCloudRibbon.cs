using System;
using System.IO;
using System.Reflection;
using System.Windows.Media.Imaging;
using Autodesk.Revit.UI;
using RevitPointCloudPlugin.UI.Commands;

namespace RevitPointCloudPlugin.UI.Ribbon
{
    /// <summary>
    /// Creates and manages the Point Cloud ribbon tab in Revit
    /// </summary>
    public class PointCloudRibbon : IDisposable
    {
        private RibbonTab _ribbonTab;
        private bool _disposed = false;

        /// <summary>
        /// Creates the ribbon interface in Revit
        /// </summary>
        /// <param name="application">The Revit UI application</param>
        public void CreateRibbon(UIControlledApplication application)
        {
            try
            {
                // Create the ribbon tab
                const string tabName = "Point Cloud";
                application.CreateRibbonTab(tabName);
                _ribbonTab = application.GetRibbonTabs().Find(tab => tab.Name == tabName);

                // Create panels
                CreateImportPanel(application, tabName);
                CreateVisualizationPanel(application, tabName);
                CreateAnalysisPanel(application, tabName);
                CreateSettingsPanel(application, tabName);
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Point Cloud Plugin Error", 
                    $"Failed to create ribbon interface:\n{ex.Message}");
            }
        }

        /// <summary>
        /// Creates the Import panel with point cloud loading commands
        /// </summary>
        private void CreateImportPanel(UIControlledApplication application, string tabName)
        {
            var panel = application.CreateRibbonPanel(tabName, "Import");

            // Import Point Cloud button
            var importButtonData = new PushButtonData(
                "ImportPointCloud",
                "Import\nPoint Cloud",
                Assembly.GetExecutingAssembly().Location,
                typeof(ImportPointCloudCommand).FullName)
            {
                LargeImage = GetEmbeddedImage("import_large.png"),
                Image = GetEmbeddedImage("import_small.png"),
                ToolTip = "Import point cloud files (.las, .laz, .e57)",
                LongDescription = "Import and visualize point cloud data from LAS, LAZ, or E57 files. " +
                                 "The plugin uses advanced streaming technology to handle massive datasets efficiently."
            };

            var importButton = panel.AddItem(importButtonData) as PushButton;
            importButton.SetContextualHelp(new ContextualHelp(
                ContextualHelpType.Url, 
                "https://docs.cloudpointforrevit.com/import"));

            // Recent Files dropdown
            var recentFilesData = new SplitButtonData("RecentFiles", "Recent Files");
            var recentFilesButton = panel.AddItem(recentFilesData) as SplitButton;
            recentFilesButton.IsSynchronizedWithCurrentItem = false;

            // Add sample recent file (will be populated dynamically)
            var sampleRecentData = new PushButtonData(
                "SampleRecent",
                "No recent files",
                Assembly.GetExecutingAssembly().Location,
                typeof(ImportPointCloudCommand).FullName);
            recentFilesButton.AddPushButton(sampleRecentData);
        }

        /// <summary>
        /// Creates the Visualization panel with display controls
        /// </summary>
        private void CreateVisualizationPanel(UIControlledApplication application, string tabName)
        {
            var panel = application.CreateRibbonPanel(tabName, "Visualization");

            // Color Mode dropdown
            var colorModeData = new ComboBoxData("ColorMode");
            var colorModeCombo = panel.AddItem(colorModeData) as ComboBox;
            colorModeCombo.ItemText = "Color Mode";
            colorModeCombo.ToolTip = "Select point cloud coloring mode";

            // Add color mode options
            var colorModes = new[]
            {
                new ComboBoxMemberData("RGB", "RGB Colors"),
                new ComboBoxMemberData("Intensity", "Intensity"),
                new ComboBoxMemberData("Classification", "Classification"),
                new ComboBoxMemberData("Elevation", "Elevation"),
                new ComboBoxMemberData("ReturnNumber", "Return Number")
            };

            foreach (var mode in colorModes)
            {
                colorModeCombo.AddItem(mode);
            }
            colorModeCombo.Current = colorModeCombo.GetItems()[0];

            // Point Size slider (will be implemented as a custom control)
            var pointSizeData = new TextBoxData("PointSize");
            var pointSizeTextBox = panel.AddItem(pointSizeData) as TextBox;
            pointSizeTextBox.Value = "1.0";
            pointSizeTextBox.ToolTip = "Point display size";
            pointSizeTextBox.PromptText = "Size";

            // Visibility toggle
            var visibilityData = new ToggleButtonData(
                "ToggleVisibility",
                "Show/Hide",
                Assembly.GetExecutingAssembly().Location,
                typeof(ToggleVisibilityCommand).FullName)
            {
                LargeImage = GetEmbeddedImage("visibility_large.png"),
                Image = GetEmbeddedImage("visibility_small.png"),
                ToolTip = "Toggle point cloud visibility"
            };

            panel.AddItem(visibilityData);
        }

        /// <summary>
        /// Creates the Analysis panel with measurement tools
        /// </summary>
        private void CreateAnalysisPanel(UIControlledApplication application, string tabName)
        {
            var panel = application.CreateRibbonPanel(tabName, "Analysis");

            // Measure Distance button
            var measureData = new PushButtonData(
                "MeasureDistance",
                "Measure\nDistance",
                Assembly.GetExecutingAssembly().Location,
                typeof(MeasureDistanceCommand).FullName)
            {
                LargeImage = GetEmbeddedImage("measure_large.png"),
                Image = GetEmbeddedImage("measure_small.png"),
                ToolTip = "Measure distances in point cloud"
            };

            panel.AddItem(measureData);

            // Section Box button
            var sectionData = new PushButtonData(
                "CreateSection",
                "Section\nBox",
                Assembly.GetExecutingAssembly().Location,
                typeof(CreateSectionCommand).FullName)
            {
                LargeImage = GetEmbeddedImage("section_large.png"),
                Image = GetEmbeddedImage("section_small.png"),
                ToolTip = "Create section box for point cloud analysis"
            };

            panel.AddItem(sectionData);

            // Statistics button
            var statsData = new PushButtonData(
                "ShowStatistics",
                "Statistics",
                Assembly.GetExecutingAssembly().Location,
                typeof(ShowStatisticsCommand).FullName)
            {
                Image = GetEmbeddedImage("stats_small.png"),
                ToolTip = "Show point cloud statistics"
            };

            panel.AddItem(statsData);
        }

        /// <summary>
        /// Creates the Settings panel with configuration options
        /// </summary>
        private void CreateSettingsPanel(UIControlledApplication application, string tabName)
        {
            var panel = application.CreateRibbonPanel(tabName, "Settings");

            // Performance Settings button
            var perfData = new PushButtonData(
                "PerformanceSettings",
                "Performance\nSettings",
                Assembly.GetExecutingAssembly().Location,
                typeof(PerformanceSettingsCommand).FullName)
            {
                LargeImage = GetEmbeddedImage("settings_large.png"),
                Image = GetEmbeddedImage("settings_small.png"),
                ToolTip = "Configure performance and memory settings"
            };

            panel.AddItem(perfData);

            // Help button
            var helpData = new PushButtonData(
                "Help",
                "Help",
                Assembly.GetExecutingAssembly().Location,
                typeof(HelpCommand).FullName)
            {
                Image = GetEmbeddedImage("help_small.png"),
                ToolTip = "Open help documentation"
            };

            panel.AddItem(helpData);

            // About button
            var aboutData = new PushButtonData(
                "About",
                "About",
                Assembly.GetExecutingAssembly().Location,
                typeof(AboutCommand).FullName)
            {
                Image = GetEmbeddedImage("about_small.png"),
                ToolTip = "About Point Cloud Plugin"
            };

            panel.AddItem(aboutData);
        }

        /// <summary>
        /// Gets an embedded image resource or returns a placeholder
        /// </summary>
        private BitmapImage GetEmbeddedImage(string imageName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var resourceName = $"RevitPointCloudPlugin.Resources.Images.{imageName}";
                
                using (var stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        var bitmap = new BitmapImage();
                        bitmap.BeginInit();
                        bitmap.StreamSource = stream;
                        bitmap.CacheOption = BitmapCacheOption.OnLoad;
                        bitmap.EndInit();
                        bitmap.Freeze();
                        return bitmap;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load image {imageName}: {ex.Message}");
            }

            // Return placeholder image
            return CreatePlaceholderImage();
        }

        /// <summary>
        /// Creates a simple placeholder image when embedded resources are not available
        /// </summary>
        private BitmapImage CreatePlaceholderImage()
        {
            // Create a simple 16x16 placeholder bitmap
            var bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.UriSource = new Uri("pack://application:,,,/RevitPointCloudPlugin;component/Resources/placeholder.png", UriKind.Absolute);
            bitmap.DecodePixelWidth = 16;
            bitmap.DecodePixelHeight = 16;
            bitmap.EndInit();
            bitmap.Freeze();
            return bitmap;
        }

        /// <summary>
        /// Disposes of ribbon resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _ribbonTab = null;
                }
                _disposed = true;
            }
        }
    }
}
