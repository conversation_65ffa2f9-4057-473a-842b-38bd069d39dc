using System;
using System.Threading;
using System.Threading.Tasks;

namespace RevitPointCloudPlugin.Core.DataIngestion
{
    /// <summary>
    /// Interface for point cloud file readers
    /// Supports asynchronous loading with progress reporting and cancellation
    /// </summary>
    public interface IPointCloudReader
    {
        /// <summary>
        /// Gets the supported file extensions for this reader
        /// </summary>
        string[] SupportedExtensions { get; }

        /// <summary>
        /// Event fired during loading to report progress
        /// </summary>
        event EventHandler<PointCloudLoadProgressEventArgs> ProgressChanged;

        /// <summary>
        /// Checks if the reader can handle the specified file
        /// </summary>
        /// <param name="filePath">Path to the point cloud file</param>
        /// <returns>True if the file can be read by this reader</returns>
        bool CanRead(string filePath);

        /// <summary>
        /// Reads metadata from the point cloud file without loading all points
        /// </summary>
        /// <param name="filePath">Path to the point cloud file</param>
        /// <returns>Point cloud metadata</returns>
        Task<PointCloudMetadata> ReadMetadataAsync(string filePath);

        /// <summary>
        /// Loads the complete point cloud data from file
        /// </summary>
        /// <param name="filePath">Path to the point cloud file</param>
        /// <param name="cancellationToken">Token to cancel the operation</param>
        /// <returns>Complete point cloud data</returns>
        Task<PointCloudData> LoadAsync(string filePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Loads point cloud data with optional downsampling
        /// </summary>
        /// <param name="filePath">Path to the point cloud file</param>
        /// <param name="downsampleRatio">Ratio for downsampling (0.1 = 10% of points)</param>
        /// <param name="cancellationToken">Token to cancel the operation</param>
        /// <returns>Downsampled point cloud data</returns>
        Task<PointCloudData> LoadWithDownsamplingAsync(string filePath, double downsampleRatio, CancellationToken cancellationToken = default);

        /// <summary>
        /// Loads points within a specific bounding box only
        /// </summary>
        /// <param name="filePath">Path to the point cloud file</param>
        /// <param name="bounds">Bounding box to filter points</param>
        /// <param name="cancellationToken">Token to cancel the operation</param>
        /// <returns>Filtered point cloud data</returns>
        Task<PointCloudData> LoadBoundsAsync(string filePath, Autodesk.Revit.DB.BoundingBoxXYZ bounds, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Factory for creating appropriate point cloud readers based on file type
    /// </summary>
    public static class PointCloudReaderFactory
    {
        /// <summary>
        /// Creates a reader for the specified file
        /// </summary>
        /// <param name="filePath">Path to the point cloud file</param>
        /// <returns>Appropriate reader instance or null if format not supported</returns>
        public static IPointCloudReader CreateReader(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentNullException(nameof(filePath));

            string extension = System.IO.Path.GetExtension(filePath).ToLowerInvariant();

            switch (extension)
            {
                case ".las":
                case ".laz":
                    return new LasReader();
                case ".e57":
                    return new E57Reader();
                default:
                    return null;
            }
        }

        /// <summary>
        /// Gets all supported file extensions
        /// </summary>
        public static string[] GetSupportedExtensions()
        {
            return new[] { ".las", ".laz", ".e57" };
        }

        /// <summary>
        /// Creates a file filter string for dialogs
        /// </summary>
        public static string GetFileFilter()
        {
            return "Point Cloud Files|*.las;*.laz;*.e57|LAS Files|*.las;*.laz|E57 Files|*.e57|All Files|*.*";
        }
    }
}
