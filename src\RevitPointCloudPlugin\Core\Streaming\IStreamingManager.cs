using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using RevitPointCloudPlugin.Core.DataIngestion;
using RevitPointCloudPlugin.Core.SpatialIndex;

namespace RevitPointCloudPlugin.Core.Streaming
{
    /// <summary>
    /// Interface for out-of-core streaming management
    /// Handles loading and unloading of point cloud data based on view frustum and LOD requirements
    /// </summary>
    public interface IStreamingManager
    {
        /// <summary>
        /// Gets or sets the maximum memory usage for streaming cache
        /// </summary>
        long MaxCacheMemory { get; set; }

        /// <summary>
        /// Gets the current cache memory usage
        /// </summary>
        long CurrentCacheMemory { get; }

        /// <summary>
        /// Gets or sets the streaming quality level (0-10, higher = better quality)
        /// </summary>
        int QualityLevel { get; set; }

        /// <summary>
        /// Event fired when streaming data is updated
        /// </summary>
        event EventHandler<StreamingUpdateEventArgs> StreamingUpdated;

        /// <summary>
        /// Initializes the streaming manager with a point cloud dataset
        /// </summary>
        /// <param name="pointCloudData">Point cloud data to stream</param>
        /// <param name="spatialIndex">Spatial index for efficient queries</param>
        Task InitializeAsync(PointCloudData pointCloudData, IOctree spatialIndex);

        /// <summary>
        /// Updates streaming based on current view parameters
        /// </summary>
        /// <param name="viewFrustum">Current view frustum</param>
        /// <param name="cameraPosition">Camera position</param>
        /// <param name="levelOfDetail">Required level of detail</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task UpdateStreamingAsync(BoundingBoxXYZ viewFrustum, XYZ cameraPosition, int levelOfDetail, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets currently loaded points for rendering
        /// </summary>
        /// <returns>Points ready for rendering</returns>
        IEnumerable<PointCloudPoint> GetLoadedPoints();

        /// <summary>
        /// Gets loaded points within a specific bounding box
        /// </summary>
        /// <param name="bounds">Bounding box to query</param>
        /// <returns>Points within bounds</returns>
        IEnumerable<PointCloudPoint> GetLoadedPoints(BoundingBoxXYZ bounds);

        /// <summary>
        /// Preloads data for a specific area
        /// </summary>
        /// <param name="bounds">Area to preload</param>
        /// <param name="priority">Loading priority</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task PreloadAsync(BoundingBoxXYZ bounds, StreamingPriority priority = StreamingPriority.Normal, CancellationToken cancellationToken = default);

        /// <summary>
        /// Clears all cached data
        /// </summary>
        void ClearCache();

        /// <summary>
        /// Gets streaming statistics
        /// </summary>
        StreamingStatistics GetStatistics();
    }

    /// <summary>
    /// Streaming priority levels
    /// </summary>
    public enum StreamingPriority
    {
        Low,
        Normal,
        High,
        Critical
    }

    /// <summary>
    /// Event arguments for streaming updates
    /// </summary>
    public class StreamingUpdateEventArgs : EventArgs
    {
        public long PointsLoaded { get; set; }
        public long PointsUnloaded { get; set; }
        public long TotalPointsInView { get; set; }
        public double LoadingProgress { get; set; }
        public string StatusMessage { get; set; }
    }

    /// <summary>
    /// Streaming performance statistics
    /// </summary>
    public class StreamingStatistics
    {
        public long TotalPointsManaged { get; set; }
        public long PointsCurrentlyLoaded { get; set; }
        public long CacheMemoryUsage { get; set; }
        public long MaxCacheMemory { get; set; }
        public double CacheHitRatio { get; set; }
        public int ActiveStreamingTasks { get; set; }
        public TimeSpan AverageLoadTime { get; set; }
        public long PointsLoadedPerSecond { get; set; }

        public override string ToString()
        {
            return $"Streaming Stats: {PointsCurrentlyLoaded:N0} points loaded, " +
                   $"Cache: {CacheMemoryUsage / (1024 * 1024):N0}MB / {MaxCacheMemory / (1024 * 1024):N0}MB, " +
                   $"Hit Ratio: {CacheHitRatio:P1}";
        }
    }

    /// <summary>
    /// Configuration for streaming behavior
    /// </summary>
    public class StreamingConfig
    {
        /// <summary>
        /// Maximum memory for streaming cache in bytes
        /// </summary>
        public long MaxCacheMemory { get; set; } = 1024 * 1024 * 1024; // 1GB

        /// <summary>
        /// Number of background loading threads
        /// </summary>
        public int LoadingThreads { get; set; } = Environment.ProcessorCount;

        /// <summary>
        /// Distance threshold for LOD switching
        /// </summary>
        public double[] LODDistances { get; set; } = { 10, 50, 200, 1000 };

        /// <summary>
        /// Points per node at each LOD level
        /// </summary>
        public int[] LODPointCounts { get; set; } = { 1000, 500, 100, 10 };

        /// <summary>
        /// Enable predictive loading based on camera movement
        /// </summary>
        public bool EnablePredictiveLoading { get; set; } = true;

        /// <summary>
        /// Frustum expansion factor for preloading
        /// </summary>
        public double FrustumExpansionFactor { get; set; } = 1.5;

        /// <summary>
        /// Maximum time to spend on streaming per frame (milliseconds)
        /// </summary>
        public int MaxStreamingTimePerFrame { get; set; } = 16; // ~60 FPS

        /// <summary>
        /// Enable compression for cached data
        /// </summary>
        public bool EnableCompression { get; set; } = true;
    }

    /// <summary>
    /// Represents a streaming chunk of point cloud data
    /// </summary>
    public class StreamingChunk
    {
        public string Id { get; set; }
        public BoundingBoxXYZ Bounds { get; set; }
        public int LevelOfDetail { get; set; }
        public long PointCount { get; set; }
        public DateTime LastAccessed { get; set; }
        public StreamingChunkState State { get; set; }
        public byte[] CompressedData { get; set; }
        public List<PointCloudPoint> Points { get; set; }

        public long MemoryUsage => CompressedData?.Length ?? (Points?.Count * 64 ?? 0); // Rough estimate
    }

    /// <summary>
    /// States of streaming chunks
    /// </summary>
    public enum StreamingChunkState
    {
        NotLoaded,
        Loading,
        Loaded,
        Compressed,
        Unloading
    }

    /// <summary>
    /// Interface for streaming chunk cache
    /// </summary>
    public interface IStreamingCache
    {
        /// <summary>
        /// Gets or sets the maximum cache size in bytes
        /// </summary>
        long MaxSize { get; set; }

        /// <summary>
        /// Gets the current cache size in bytes
        /// </summary>
        long CurrentSize { get; }

        /// <summary>
        /// Adds a chunk to the cache
        /// </summary>
        bool TryAdd(string key, StreamingChunk chunk);

        /// <summary>
        /// Gets a chunk from the cache
        /// </summary>
        bool TryGet(string key, out StreamingChunk chunk);

        /// <summary>
        /// Removes a chunk from the cache
        /// </summary>
        bool TryRemove(string key);

        /// <summary>
        /// Clears all cached chunks
        /// </summary>
        void Clear();

        /// <summary>
        /// Gets cache statistics
        /// </summary>
        CacheStatistics GetStatistics();
    }

    /// <summary>
    /// Cache performance statistics
    /// </summary>
    public class CacheStatistics
    {
        public long TotalRequests { get; set; }
        public long CacheHits { get; set; }
        public long CacheMisses { get; set; }
        public double HitRatio => TotalRequests > 0 ? (double)CacheHits / TotalRequests : 0;
        public int ChunkCount { get; set; }
        public long MemoryUsage { get; set; }
        public long MaxMemory { get; set; }
    }
}
