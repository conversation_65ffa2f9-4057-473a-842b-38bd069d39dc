using System;
using Autodesk.Revit.DB;

namespace RevitPointCloudPlugin.Utils
{
    /// <summary>
    /// Utility class for coordinate system transformations
    /// Handles conversion between different coordinate systems and Revit's internal coordinate system
    /// </summary>
    public static class CoordinateTransform
    {
        /// <summary>
        /// Creates an identity transformation matrix
        /// </summary>
        public static Transform CreateIdentityTransform()
        {
            return Transform.Identity;
        }

        /// <summary>
        /// Creates a transformation from point cloud coordinate system to Revit coordinate system
        /// </summary>
        /// <param name="scale">Scale factors for X, Y, Z axes</param>
        /// <param name="offset">Offset values for X, Y, Z axes</param>
        /// <param name="rotation">Optional rotation matrix</param>
        /// <returns>Transformation matrix</returns>
        public static Transform CreatePointCloudToRevitTransform(XYZ scale, XYZ offset, Transform rotation = null)
        {
            if (scale == null) throw new ArgumentNullException(nameof(scale));
            if (offset == null) throw new ArgumentNullException(nameof(offset));

            // Create scaling transform
            var scaleTransform = Transform.CreateScale(scale);
            
            // Create translation transform
            var translationTransform = Transform.CreateTranslation(offset);
            
            // Combine transformations
            var result = translationTransform.Multiply(scaleTransform);
            
            // Apply rotation if provided
            if (rotation != null)
            {
                result = result.Multiply(rotation);
            }
            
            return result;
        }

        /// <summary>
        /// Creates a transformation to convert from geographic coordinates to Revit coordinates
        /// </summary>
        /// <param name="originLatitude">Origin latitude in degrees</param>
        /// <param name="originLongitude">Origin longitude in degrees</param>
        /// <param name="originElevation">Origin elevation in meters</param>
        /// <returns>Transformation matrix</returns>
        public static Transform CreateGeographicToRevitTransform(double originLatitude, double originLongitude, double originElevation)
        {
            // Convert to radians
            var latRad = originLatitude * Math.PI / 180.0;
            var lonRad = originLongitude * Math.PI / 180.0;

            // Earth radius in meters
            const double earthRadius = 6378137.0;

            // Calculate local coordinate system origin in ECEF (Earth-Centered, Earth-Fixed)
            var cosLat = Math.Cos(latRad);
            var sinLat = Math.Sin(latRad);
            var cosLon = Math.Cos(lonRad);
            var sinLon = Math.Sin(lonRad);

            // Create transformation matrix for local tangent plane
            // This is a simplified transformation - in production, use proper geodetic libraries
            var origin = new XYZ(0, 0, 0);
            var basisX = new XYZ(-sinLon, cosLon, 0);
            var basisY = new XYZ(-sinLat * cosLon, -sinLat * sinLon, cosLat);
            var basisZ = new XYZ(cosLat * cosLon, cosLat * sinLon, sinLat);

            return Transform.CreateTranslation(origin);
        }

        /// <summary>
        /// Creates a transformation for UTM coordinates to Revit coordinates
        /// </summary>
        /// <param name="utmZone">UTM zone number</param>
        /// <param name="isNorthernHemisphere">True for northern hemisphere</param>
        /// <param name="falseEasting">False easting value</param>
        /// <param name="falseNorthing">False northing value</param>
        /// <returns>Transformation matrix</returns>
        public static Transform CreateUTMToRevitTransform(int utmZone, bool isNorthernHemisphere, double falseEasting, double falseNorthing)
        {
            // Simplified UTM transformation
            // In production, use proper coordinate transformation libraries like Proj.NET
            
            var offset = new XYZ(-falseEasting, -falseNorthing, 0);
            var scale = new XYZ(1.0, 1.0, 1.0); // Meters to feet conversion if needed
            
            return CreatePointCloudToRevitTransform(scale, offset);
        }

        /// <summary>
        /// Applies transformation to a point
        /// </summary>
        /// <param name="point">Point to transform</param>
        /// <param name="transform">Transformation to apply</param>
        /// <returns>Transformed point</returns>
        public static XYZ TransformPoint(XYZ point, Transform transform)
        {
            if (point == null) throw new ArgumentNullException(nameof(point));
            if (transform == null) throw new ArgumentNullException(nameof(transform));

            return transform.OfPoint(point);
        }

        /// <summary>
        /// Applies transformation to a vector
        /// </summary>
        /// <param name="vector">Vector to transform</param>
        /// <param name="transform">Transformation to apply</param>
        /// <returns>Transformed vector</returns>
        public static XYZ TransformVector(XYZ vector, Transform transform)
        {
            if (vector == null) throw new ArgumentNullException(nameof(vector));
            if (transform == null) throw new ArgumentNullException(nameof(transform));

            return transform.OfVector(vector);
        }

        /// <summary>
        /// Creates a transformation to align point cloud with Revit project base point
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="pointCloudOrigin">Origin point of the point cloud</param>
        /// <returns>Transformation matrix</returns>
        public static Transform CreateAlignToProjectBaseTransform(Document document, XYZ pointCloudOrigin)
        {
            if (document == null) throw new ArgumentNullException(nameof(document));
            if (pointCloudOrigin == null) throw new ArgumentNullException(nameof(pointCloudOrigin));

            // Get project base point
            var projectBasePoint = GetProjectBasePoint(document);
            if (projectBasePoint == null)
            {
                return Transform.Identity;
            }

            // Calculate offset to align point cloud origin with project base point
            var offset = projectBasePoint.Subtract(pointCloudOrigin);
            return Transform.CreateTranslation(offset);
        }

        /// <summary>
        /// Gets the project base point from the document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Project base point location</returns>
        private static XYZ GetProjectBasePoint(Document document)
        {
            try
            {
                // Find the project base point element
                var collector = new FilteredElementCollector(document)
                    .OfClass(typeof(BasePoint));

                foreach (BasePoint basePoint in collector)
                {
                    if (basePoint.IsShared) // Project Base Point
                    {
                        return basePoint.Position;
                    }
                }

                // Fallback to origin if no project base point found
                return XYZ.Zero;
            }
            catch (Exception)
            {
                return XYZ.Zero;
            }
        }

        /// <summary>
        /// Converts feet to meters
        /// </summary>
        /// <param name="feet">Value in feet</param>
        /// <returns>Value in meters</returns>
        public static double FeetToMeters(double feet)
        {
            return feet * 0.3048;
        }

        /// <summary>
        /// Converts meters to feet
        /// </summary>
        /// <param name="meters">Value in meters</param>
        /// <returns>Value in feet</returns>
        public static double MetersToFeet(double meters)
        {
            return meters / 0.3048;
        }

        /// <summary>
        /// Converts a point from meters to feet
        /// </summary>
        /// <param name="pointInMeters">Point in meters</param>
        /// <returns>Point in feet</returns>
        public static XYZ MetersToFeet(XYZ pointInMeters)
        {
            if (pointInMeters == null) throw new ArgumentNullException(nameof(pointInMeters));

            return new XYZ(
                MetersToFeet(pointInMeters.X),
                MetersToFeet(pointInMeters.Y),
                MetersToFeet(pointInMeters.Z)
            );
        }

        /// <summary>
        /// Converts a point from feet to meters
        /// </summary>
        /// <param name="pointInFeet">Point in feet</param>
        /// <returns>Point in meters</returns>
        public static XYZ FeetToMeters(XYZ pointInFeet)
        {
            if (pointInFeet == null) throw new ArgumentNullException(nameof(pointInFeet));

            return new XYZ(
                FeetToMeters(pointInFeet.X),
                FeetToMeters(pointInFeet.Y),
                FeetToMeters(pointInFeet.Z)
            );
        }

        /// <summary>
        /// Creates a rotation transform around the Z-axis
        /// </summary>
        /// <param name="angleRadians">Rotation angle in radians</param>
        /// <param name="center">Center point of rotation</param>
        /// <returns>Rotation transformation</returns>
        public static Transform CreateRotationZ(double angleRadians, XYZ center = null)
        {
            center = center ?? XYZ.Zero;
            var axis = XYZ.BasisZ;
            return Transform.CreateRotationAtPoint(axis, angleRadians, center);
        }

        /// <summary>
        /// Creates a compound transformation for typical point cloud alignment
        /// </summary>
        /// <param name="translation">Translation vector</param>
        /// <param name="rotation">Rotation angle in radians around Z-axis</param>
        /// <param name="scale">Uniform scale factor</param>
        /// <returns>Compound transformation</returns>
        public static Transform CreateCompoundTransform(XYZ translation, double rotation, double scale)
        {
            // Apply transformations in order: scale, rotate, translate
            var scaleTransform = Transform.CreateScale(scale);
            var rotationTransform = CreateRotationZ(rotation);
            var translationTransform = Transform.CreateTranslation(translation ?? XYZ.Zero);

            // Combine transformations
            var result = scaleTransform;
            result = result.Multiply(rotationTransform);
            result = result.Multiply(translationTransform);

            return result;
        }
    }

    /// <summary>
    /// Coordinate system information for point clouds
    /// </summary>
    public class CoordinateSystemInfo
    {
        public string Name { get; set; }
        public string EPSG { get; set; }
        public string WKT { get; set; }
        public CoordinateSystemType Type { get; set; }
        public string Units { get; set; }
        public XYZ Origin { get; set; }
        public double[] Parameters { get; set; }
    }

    /// <summary>
    /// Types of coordinate systems
    /// </summary>
    public enum CoordinateSystemType
    {
        Unknown,
        Geographic,
        UTM,
        StatePlane,
        Local,
        ProjectBased
    }
}
